"""FPISZ MCP工具函数.

提供给MCP服务器调用的异步工具函数，包括设备控制、界面跳转、数据播报等功能
"""

import asyncio
import json
from typing import Any, Dict

from src.utils.logging_config import get_logger

logger = get_logger(__name__)


async def _send_broadcast_message(message: str):
    """发送播报消息.

    Args:
        message: 要播报的消息内容
    """
    try:
        from src.application import Application

        app = Application.get_instance()
        logger.info(f"[FPISZ] 播报消息: {message}")
        await app._send_text_tts(message)
    except Exception as e:
        logger.warning(f"[FPISZ] 播报消息失败: {e}")


async def control_device(args: Dict[str, Any]) -> str:
    """控制设备动作.

    Args:
        args: 包含以下参数的字典
            - device: 设备类型（包括灯、空调、门禁、排风扇等）
            - action: 控制类型（打开或关闭，调节温度等）

    Returns:
        str: JSON格式的控制结果
    """
    try:
        device = args["device"]
        action = args["action"]

        logger.info(f"[FPISZ] 控制设备: {device}, 动作: {action}")

        # 模拟异步设备控制操作
        await asyncio.sleep(0.1)  # 模拟设备响应时间

        # 设备控制逻辑
        if "打开" in action:
            logger.info(f"[FPISZ] 打开{device}")
            broadcast_msg = f"{device}已打开"
            success = True
        elif "关闭" in action:
            logger.info(f"[FPISZ] 关闭{device}")
            broadcast_msg = f"{device}已关闭"
            success = True
        elif "调节" in action or "温度" in action:
            logger.info(f"[FPISZ] 调节{device}温度")
            broadcast_msg = f"{device}温度调节完成"
            success = True
        else:
            logger.warning(f"[FPISZ] 未知的控制动作: {action}")
            broadcast_msg = f"无法识别的{device}控制指令"
            success = False

        # 发送播报消息
        await _send_broadcast_message(broadcast_msg)

        # 构建返回结果
        if success:
            result = {
                "success": True,
                "message": f"{device}{action}成功",
                "device": device,
                "action": action,
                "broadcast_message": broadcast_msg
            }
        else:
            result = {
                "success": False,
                "message": f"未知的控制动作: {action}",
                "device": device,
                "action": action,
                "broadcast_message": broadcast_msg
            }

        logger.info(f"[FPISZ] 设备控制结果: {result['success']}")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except KeyError as e:
        error_msg = f"缺少必需参数: {e}"
        logger.error(f"[FPISZ] {error_msg}")
        await _send_broadcast_message("设备控制参数错误")
        return json.dumps({"success": False, "message": error_msg}, ensure_ascii=False)
    except Exception as e:
        error_msg = f"设备控制失败: {str(e)}"
        logger.error(f"[FPISZ] {error_msg}", exc_info=True)
        await _send_broadcast_message("设备控制操作失败")
        return json.dumps({"success": False, "message": error_msg}, ensure_ascii=False)


async def navigate_to_interface(args: Dict[str, Any]) -> str:
    """导航到指定界面.

    Args:
        args: 包含以下参数的字典
            - interface_type: 界面类型（如实时数据查询界面、智能巡检界面等）

    Returns:
        str: JSON格式的导航结果
    """
    try:
        interface_type = args["interface_type"]

        logger.info(f"[FPISZ] 导航到界面: {interface_type}")

        # 模拟异步界面跳转操作
        await asyncio.sleep(0.2)  # 模拟界面加载时间

        # 界面跳转逻辑
        if "实时数据查询" in interface_type:
            logger.info("[FPISZ] 导航到实时数据查询界面")
            broadcast_msg = "已打开实时数据查询界面"
            success = True
        elif "智能巡检" in interface_type:
            logger.info("[FPISZ] 导航到智能巡检界面")
            broadcast_msg = "已打开智能巡检界面"
            success = True
        elif "设备查询" in interface_type:
            logger.info("[FPISZ] 导航到设备查询界面")
            broadcast_msg = "已打开设备查询界面"
            success = True
        elif "动环" in interface_type:
            logger.info("[FPISZ] 导航到动环界面")
            broadcast_msg = "已打开动环监控界面"
            success = True
        elif any(keyword in interface_type for keyword in ["高锰酸盐", "五参数", "总磷", "总氮", "氨氮"]):
            logger.info(f"[FPISZ] 导航到仪表设备界面: {interface_type}")
            # 提取关键词用于播报
            for keyword in ["高锰酸盐", "五参数", "总磷", "总氮", "氨氮"]:
                if keyword in interface_type:
                    broadcast_msg = f"已打开{keyword}仪表界面"
                    break
            else:
                broadcast_msg = "已打开仪表设备界面"
            success = True
        else:
            logger.warning(f"[FPISZ] 未知的界面类型: {interface_type}")
            broadcast_msg = "无法识别的界面类型"
            success = False

        # 发送播报消息
        await _send_broadcast_message(broadcast_msg)

        # 构建返回结果
        if success:
            result = {
                "success": True,
                "message": f"{interface_type}导航成功",
                "interface_type": interface_type,
                "broadcast_message": broadcast_msg
            }
        else:
            result = {
                "success": False,
                "message": f"未知的界面类型: {interface_type}",
                "interface_type": interface_type,
                "broadcast_message": broadcast_msg
            }

        logger.info(f"[FPISZ] 界面导航结果: {result['success']}")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except KeyError as e:
        error_msg = f"缺少必需参数: {e}"
        logger.error(f"[FPISZ] {error_msg}")
        await _send_broadcast_message("界面导航参数错误")
        return json.dumps({"success": False, "message": error_msg}, ensure_ascii=False)
    except Exception as e:
        error_msg = f"界面导航失败: {str(e)}"
        logger.error(f"[FPISZ] {error_msg}", exc_info=True)
        await _send_broadcast_message("界面导航操作失败")
        return json.dumps({"success": False, "message": error_msg}, ensure_ascii=False)


async def broadcast_data(args: Dict[str, Any]) -> str:
    """播报数据信息.

    Args:
        args: 包含以下参数的字典
            - data_type: 数据类型（如当前监测数据、智能巡检结果等）

    Returns:
        str: JSON格式的播报结果
    """
    try:
        data_type = args["data_type"]

        logger.info(f"[FPISZ] 播报数据: {data_type}")

        # 模拟异步数据获取和播报操作
        await asyncio.sleep(0.3)  # 模拟数据查询时间

        # 数据播报逻辑
        if "实时数据" in data_type or "监测数据" in data_type:
            logger.info("[FPISZ] 播报实时监测数据")
            broadcast_msg = "当前监测数据：水温25度，pH值7.2，溶解氧8.5毫克每升"
            success = True
        elif "智能巡检" in data_type:
            logger.info("[FPISZ] 播报智能巡检结果")
            broadcast_msg = "智能巡检完成，所有设备运行正常"
            success = True
        elif "站点信息" in data_type:
            logger.info("[FPISZ] 播报站点信息")
            broadcast_msg = "站点运行正常，设备在线率100%"
            success = True
        elif "水质超标" in data_type:
            logger.info("[FPISZ] 播报水质超标情况")
            broadcast_msg = "近一个月无水质超标情况"
            success = True
        elif "温度" in data_type and "湿度" in data_type:
            logger.info("[FPISZ] 播报温湿度信息")
            broadcast_msg = "当前环境温度22度，湿度65%"
            success = True
        elif "设备报警" in data_type:
            logger.info("[FPISZ] 播报设备报警信息")
            broadcast_msg = "当前无设备报警信息"
            success = True
        elif any(keyword in data_type for keyword in ["高锰酸盐", "氨氮", "总磷", "总氮", "五参数"]):
            logger.info(f"[FPISZ] 播报水质参数数据: {data_type}")
            # 根据具体参数生成播报内容
            for keyword in ["高锰酸盐", "氨氮", "总磷", "总氮", "五参数"]:
                if keyword in data_type:
                    if keyword == "高锰酸盐":
                        broadcast_msg = "高锰酸盐指数3.2毫克每升，数值正常"
                    elif keyword == "氨氮":
                        broadcast_msg = "氨氮浓度0.8毫克每升，数值正常"
                    elif keyword == "总磷":
                        broadcast_msg = "总磷浓度0.15毫克每升，数值正常"
                    elif keyword == "总氮":
                        broadcast_msg = "总氮浓度1.2毫克每升，数值正常"
                    elif keyword == "五参数":
                        broadcast_msg = "五参数检测完成，所有指标均在正常范围内"
                    break
            else:
                broadcast_msg = "水质参数检测完成，数值正常"
            success = True
        elif "运维" in data_type:
            logger.info("[FPISZ] 播报运维内容")
            broadcast_msg = "当前无需要运维的设备"
            success = True
        else:
            logger.warning(f"[FPISZ] 未知的数据类型: {data_type}")
            broadcast_msg = "无法识别的数据类型"
            success = False

        # 发送播报消息
        await _send_broadcast_message(broadcast_msg)

        # 构建返回结果
        if success:
            result = {
                "success": True,
                "message": f"{data_type}播报成功",
                "data_type": data_type,
                "broadcast_message": broadcast_msg
            }
        else:
            result = {
                "success": False,
                "message": f"未知的数据类型: {data_type}",
                "data_type": data_type,
                "broadcast_message": broadcast_msg
            }

        logger.info(f"[FPISZ] 数据播报结果: {result['success']}")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except KeyError as e:
        error_msg = f"缺少必需参数: {e}"
        logger.error(f"[FPISZ] {error_msg}")
        await _send_broadcast_message("数据播报参数错误")
        return json.dumps({"success": False, "message": error_msg}, ensure_ascii=False)
    except Exception as e:
        error_msg = f"数据播报失败: {str(e)}"
        logger.error(f"[FPISZ] {error_msg}", exc_info=True)
        await _send_broadcast_message("数据播报操作失败")
        return json.dumps({"success": False, "message": error_msg}, ensure_ascii=False)
