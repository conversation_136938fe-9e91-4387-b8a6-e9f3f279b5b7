﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ModelContextProtocol.Server;
using ServerDemo;
using System.ComponentModel;
using System.Text;

try
{
    // 输出启动信息  
    LogHelper.Log("正在启动MCPServer");

    // 创建一个空的应用程序构建器  
    var builder = Host.CreateEmptyApplicationBuilder(settings: null);

    // 配置服务：添加MCP服务器，使用标准输入输出传输协议，并加载程序集中的工具  
    builder.Services
        .AddMcpServer()
        .WithStdioServerTransport()
         .WithToolsFromAssembly();

    Console.WriteLine("完成构建");

    // 构建并运行应用程序  
    await builder.Build().RunAsync();

    return 0;
}
catch (Exception ex)
{
    // 捕获异常并输出错误信息  

    Console.WriteLine($"主机意外终止: {ex.Message}");
    return 1;
}

// 定义一个工具类，标记为MCP服务器工具类型  
[McpServerToolType]
public class ControlFS
{
    // 定义一个工具方法，获取当前高锰酸盐指数
    [McpServerTool, Description("控制某个设备动作，设备包括灯、空调、门禁、排风扇等，动作包括打开或关闭，调节温度等")]
    public static string ControlFengshan([Description("设备类型(包括灯、空调、门禁、排风扇等)")] string device, [Description("控制类型（打开或关闭，调节温度等）")] string type)
    {
        try
        {        
            if (type.Contains("打开"))
            {
                LogHelper.Log($"打开{device}");
              
            }
            else if (type.Contains("关闭"))
            {
                LogHelper.Log($"关闭{device}");
              
            }
            return $"{device}{type}成功";
        }
        catch (Exception ex)
        {
            LogHelper.Log($"{ex.Message}");
            return $"{device}{type}失败";
        }
    }
}

// 定义一个工具类，标记为MCP服务器工具类型  
[McpServerToolType]
public class UITiaozhuan
{
    // 定义一个工具方法，获取当前高锰酸盐指数
    [McpServerTool, Description("打开到某个界面，如实时数据查询界面、智能巡检界面、设备查询界面、动环界面、高锰酸盐指数仪表设备界面、五参数仪表设备界面" +
        "、总磷仪表设备界面、总氮仪表设备界面、五参数仪表设备界面、氨氮仪表设备界面等，需要识别总磷、总氮、氨氮这种专有名词")]
    public static string ControlUI([Description("某个界面，界面类型如如实时数据查询界面、智能巡检界面、设备查询界面、动环界面、" +
        "高锰酸盐指数仪表设备界面、五参数仪表设备界面、总磷仪表设备界面、总氮仪表设备界面、五参数仪表设备界面、氨氮仪表设备界面等，需要识别总磷、总氮、氨氮这种专有名词")] string type)
    {
        try
        {
            if (type.Contains("实时数据查询"))
            {
                LogHelper.Log("实时数据查询");

            }
            else if (type.Contains("智能巡检"))
            {
                LogHelper.Log("智能巡检");

            }
            return $"{type}成功";
        }
        catch (Exception ex)
        {
            LogHelper.Log($"{ex.Message}");
            return $"{type}失败";
        }
    }
}

// 定义一个工具类，标记为MCP服务器工具类型  
[McpServerToolType]
public class BoBaoShuju
{
    // 定义一个工具方法，获取当前高锰酸盐指数
    [McpServerTool, Description("播报某条数据，如当前监测数据、智能巡检结果、站点信息、最近一个月的水质超标情况、站点的温度和湿度信息、站点当前设备报警信息、" +
        "高锰酸盐指数数据、氨氮数据、总磷数据、总氮数据、当前需要运维的内容等，需要识别总磷、总氮、氨氮这种专有名词")]
    public static string BoBao([Description("某条数据，播报的数据类型。如当前监测数据、智能巡检结果、站点信息、" +
        "最近一个月的水质超标情况、站点的温度和湿度信息、站点当前设备报警信息 、高锰酸盐指数数据、氨氮数据、总磷数据、总氮数据、当前需要运维的内容等，需要识别总磷、总氮、氨氮这种专有名词")] string type)
    {
        try
        {
            if (type.Contains("实时数据查询"))
            {
                LogHelper.Log("实时数据查询");

            }
            else if (type.Contains("智能巡检"))
            {
                LogHelper.Log("智能巡检");

            }
            return $"{type}成功";
        }
        catch (Exception ex)
        {
            LogHelper.Log($"{ex.Message}");
            return $"{type}失败";
        }
    }
}

