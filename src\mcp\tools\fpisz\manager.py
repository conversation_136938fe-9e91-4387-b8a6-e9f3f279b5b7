"""FPISZ工具管理器.

负责FPISZ工具的初始化、配置和MCP工具注册
"""

from typing import Any, Dict

from src.utils.logging_config import get_logger

from .tools import broadcast_data, control_device, navigate_to_interface

logger = get_logger(__name__)


class FpiszToolsManager:
    """
    FPISZ工具管理器.
    """

    def __init__(self):
        """
        初始化FPISZ工具管理器.
        """
        self._initialized = False
        logger.info("[FpiszManager] FPISZ工具管理器初始化")

    def init_tools(self, add_tool, PropertyList, Property, PropertyType):
        """
        初始化并注册所有FPISZ工具.
        """
        try:
            logger.info("[FpiszManager] 开始注册FPISZ工具")

            # 注册设备控制工具
            self._register_device_control_tool(
                add_tool, PropertyList, Property, PropertyType
            )

            # 注册界面跳转工具
            self._register_interface_navigation_tool(
                add_tool, PropertyList, Property, PropertyType
            )

            # 注册数据播报工具
            self._register_data_broadcast_tool(
                add_tool, PropertyList, Property, PropertyType
            )

            self._initialized = True
            logger.info("[FpiszManager] FPISZ工具注册完成")

        except Exception as e:
            logger.error(f"[FpiszManager] FPISZ工具注册失败: {e}", exc_info=True)
            raise

    def _register_device_control_tool(self, add_tool, PropertyList, Property, PropertyType):
        """
        注册设备控制工具.
        """
        props = PropertyList(
            [
                Property(
                    "device",
                    PropertyType.STRING,
                ),
                Property(
                    "action",
                    PropertyType.STRING,
                ),
            ]
        )

        add_tool(
            (
                "fpisz.control_device",
                "控制某个设备动作，设备包括灯、空调、门禁、排风扇等，动作包括打开或关闭，调节温度等。"
                "适用于：设备控制、环境调节、安全管理等场景。",
                props,
                control_device,
            )
        )
        logger.debug("[FpiszManager] 注册设备控制工具成功")

    def _register_interface_navigation_tool(self, add_tool, PropertyList, Property, PropertyType):
        """
        注册界面跳转工具.
        """
        props = PropertyList(
            [
                Property(
                    "interface_type",
                    PropertyType.STRING,
                ),
            ]
        )

        add_tool(
            (
                "fpisz.navigate_interface",
                "打开到某个界面，如实时数据查询界面、智能巡检界面、设备查询界面、动环界面、"
                "高锰酸盐指数仪表设备界面、五参数仪表设备界面、总磷仪表设备界面、总氮仪表设备界面、"
                "氨氮仪表设备界面等，需要识别总磷、总氮、氨氮、高锰酸盐指数这种专有名词。"
                "适用于：界面导航、功能切换、系统操作等场景。",
                props,
                navigate_to_interface,
            )
        )
        logger.debug("[FpiszManager] 注册界面跳转工具成功")

    def _register_data_broadcast_tool(self, add_tool, PropertyList, Property, PropertyType):
        """
        注册数据播报工具.
        """
        props = PropertyList(
            [
                Property(
                    "data_type",
                    PropertyType.STRING,
                ),
            ]
        )

        add_tool(
            (
                "fpisz.broadcast_data",
                "播报某条数据，如当前监测数据、智能巡检结果、站点信息、最近一个月的水质超标情况、"
                "站点的温度和湿度信息、站点当前设备报警信息、高锰酸盐指数数据、氨氮数据、总磷数据、"
                "总氮数据、当前需要运维的内容等，需要识别总磷、总氮、氨氮这种专有名词。"
                "适用于：数据播报、状态通知、信息查询等场景。",
                props,
                broadcast_data,
            )
        )
        logger.debug("[FpiszManager] 注册数据播报工具成功")

    def is_initialized(self) -> bool:
        """
        检查管理器是否已初始化.
        """
        return self._initialized

    def get_status(self) -> Dict[str, Any]:
        """
        获取管理器状态.
        """
        return {
            "initialized": self._initialized,
            "tools_count": 3,  # 当前注册的工具数量
            "available_tools": [
                "control_device",
                "navigate_interface", 
                "broadcast_data",
            ],
        }


# 全局管理器实例
_fpisz_tools_manager = None


def get_fpisz_manager() -> FpiszToolsManager:
    """
    获取FPISZ工具管理器单例.
    """
    global _fpisz_tools_manager
    if _fpisz_tools_manager is None:
        _fpisz_tools_manager = FpiszToolsManager()
        logger.debug("[FpiszManager] 创建FPISZ工具管理器实例")
    return _fpisz_tools_manager
