{"python.formatting.provider": "black", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "always", "source.fixAll": "explicit"}, "python.formatting.blackArgs": ["--line-length", "88", "--target-version", "py39"], "python.formatting.blackPath": "/Users/<USER>/miniconda3/envs/py-test/bin/black", "python.linting.flake8Enabled": true, "python.linting.enabled": true, "python.linting.flake8Args": ["--max-line-length=88", "--extend-ignore=E203,W503,E501"], "python.sortImports.args": ["--profile=black", "--line-length=88"], "[python]": {"editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}, "autoDocstring.docstringFormat": "google", "python.analysis.autoImportCompletions": true, "python.linting.pylintEnabled": false, "python.linting.mypyEnabled": false}