# 语音唤醒功能

## 唤醒词模型

使用语音唤醒功能需要下载和配置唤醒词模型：

- [唤醒词模型下载](https://alphacephei.com/vosk/models)
- 下载完成后解压放至根目录/models
- 默认读取vosk-model-small-cn-0.22小模型
- ![Image](./images/唤醒词.png)

## 启用语音唤醒

1. 打开配置文件 `/config/config.json`
2. 修改 `WAKE_WORD_OPTIONS.USE_WAKE_WORD` 设置为 `true`
3. 可以在 `WAKE_WORD_OPTIONS.WAKE_WORDS` 数组中自定义唤醒词
4. 确保 `WAKE_WORD_OPTIONS.MODEL_PATH` 设置正确，指向您下载的模型

示例配置：
```json
{
  "WAKE_WORD_OPTIONS": {
    "USE_WAKE_WORD": true,
    "MODEL_PATH": "models/vosk-model-small-cn-0.22",
    "WAKE_WORDS": [
      "小智",
      "你好小智",
      "嘿小智"
    ]
  }
}
```

## 使用方法

1. 启动程序后，系统会加载唤醒词模型并自动进入唤醒词监听状态
2. 说出您设置的唤醒词（如"小智"），系统会自动从IDLE状态切换到LISTENING状态
3. 此时可以继续说出您的指令
4. 如果您未说出任何指令，系统会在一段时间后自动回到唤醒词监听状态

## 注意事项

1. 唤醒词模型加载需要一定时间，请耐心等待
2. 唤醒词识别准确度取决于模型质量和环境噪音
3. 可以尝试不同大小的模型，小模型速度快但准确度较低
4. 考虑使用独特的唤醒词以避免误触发
5. 使用唤醒词功能会略微增加系统资源占用