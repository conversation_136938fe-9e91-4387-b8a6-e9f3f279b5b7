<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QWidget" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>小智Ai客户端</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">

   <item>
         <widget class="QFrame" name="status_card">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="frameShape">
       <enum>QFrame::NoFrame</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Plain</enum>
      </property>
     <layout class="QVBoxLayout" name="card_layout">
      <item>
       <widget class="QLabel" name="status_label">
        <property name="font">
         <font>
          <pointsize>14</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string>
QLabel {
    color: #2196F3;
    padding: 12px 20px;
    background-color: #E3F2FD;
    border-radius: 12px;
    border: none;
    margin: 10px;
}
         </string>
        </property>
        <property name="text">
         <string>状态: 未连接</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="emotion_label">
        <property name="font">
         <font>
          <pointsize>48</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string>
QLabel {
    margin: 20px;
    padding: 20px;
    background-color: transparent;
    border: none;
}
         </string>
        </property>
        <property name="text">
         <string>😊</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="tts_text_label">
        <property name="font">
         <font>
          <pointsize>14</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string>
QLabel {
    color: #555555;
    padding: 15px 20px;
    background-color: transparent;
    border: none;
    margin: 5px 15px;
}
         </string>
        </property>
        <property name="text">
         <string>待命</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="button_layout">
     <property name="spacing">
      <number>8</number>
     </property>
     <property name="leftMargin">
      <number>10</number>
     </property>
     <property name="rightMargin">
      <number>10</number>
     </property>
     <property name="bottomMargin">
      <number>10</number>
     </property>
     <item>
      <widget class="QPushButton" name="manual_btn">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>36</height>
        </size>
       </property>
       <property name="text">
        <string>按住后说话</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="abort_btn">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>36</height>
        </size>
       </property>
       <property name="text">
        <string>打断对话</string>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="text_input_layout">
       <property name="spacing">
        <number>4</number>
       </property>
       <item>
        <widget class="QLineEdit" name="text_input">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>36</height>
          </size>
         </property>
         <property name="placeholderText">
          <string>输入文字...</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="send_btn">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>36</height>
          </size>
         </property>
         <property name="text">
          <string>发送</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QPushButton" name="auto_btn">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>36</height>
        </size>
       </property>
       <property name="text">
        <string>开始对话</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="mode_btn">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>36</height>
        </size>
       </property>
       <property name="text">
        <string>手动对话</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
